# iceAirPlay 项目创建指南

由于Xcode项目文件的复杂性，建议按照以下步骤在Xcode中手动创建项目：

## 步骤1: 在Xcode中创建新项目

1. 打开Xcode
2. 选择 "Create a new Xcode project"
3. 选择 "iOS" -> "App"
4. 填写项目信息：
   - Product Name: `iceAirPlay`
   - Interface: `Storyboard`
   - Language: `Swift`
   - Bundle Identifier: `com.iceairplay.app`
   - 取消勾选 "Use Core Data"
   - 取消勾选 "Include Tests"

5. 选择保存位置为: `/Users/<USER>/SourceCode/iceAirPlay`

## 步骤2: 替换生成的文件

创建项目后，将以下文件替换为我们已经准备好的文件：

### 需要替换的文件：
- `AppDelegate.swift` - 已准备好
- `SceneDelegate.swift` - 已准备好  
- `ViewController.swift` - 已准备好
- `Info.plist` - 已准备好
- `LaunchScreen.storyboard` - 已准备好

### 需要添加的文件：
- `AirPlayVideoPlayer.swift` - 新建文件并复制内容

## 步骤3: 配置项目设置

1. 在项目设置中，确保：
   - Deployment Target: iOS 14.0
   - Bundle Identifier: com.iceairplay.app

2. 在 "Signing & Capabilities" 中：
   - 选择你的开发团队
   - 确保自动签名已启用

## 步骤4: 添加框架

确保项目链接了以下框架（通常会自动添加）：
- AVFoundation
- AVKit
- UIKit

## 步骤5: 运行项目

1. 连接真实的iOS设备（AirPlay功能需要真实设备）
2. 选择设备作为运行目标
3. 点击运行按钮

## 当前文件状态

所有源代码文件都已经在 `iceAirPlay/` 文件夹中准备好：

```
iceAirPlay/
├── AppDelegate.swift          ✅ 已准备
├── SceneDelegate.swift        ✅ 已准备
├── ViewController.swift       ✅ 已准备
├── AirPlayVideoPlayer.swift   ✅ 已准备
├── Info.plist                ✅ 已准备
├── Assets.xcassets/          ✅ 已准备
└── Base.lproj/
    └── LaunchScreen.storyboard ✅ 已准备
```

## 替代方案：使用现有文件

如果你想直接使用现有的文件而不重新创建项目：

1. 在Xcode中创建新的iOS项目（按照步骤1）
2. 删除Xcode生成的默认文件
3. 将我们准备的文件拖拽到Xcode项目中
4. 确保所有文件都正确添加到项目target中

## 注意事项

- 确保所有Swift文件都添加到项目的target中
- 检查Info.plist文件路径是否正确设置
- 确保LaunchScreen.storyboard正确链接
- 在真实设备上测试AirPlay功能

## 功能验证

项目创建完成后，应该具备以下功能：
- ✅ 视频播放
- ✅ AirPlay投屏
- ✅ 媒体控制（播放/暂停/进度/音量）
- ✅ 自定义视频URL输入
- ✅ 示例视频快速加载

如果遇到任何问题，请检查：
1. 所有文件是否正确添加到项目中
2. Bundle Identifier是否唯一
3. 开发者证书是否正确配置
4. 设备是否连接到与Apple TV相同的WiFi网络
