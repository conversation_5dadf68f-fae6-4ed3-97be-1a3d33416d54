{"": {"diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-master.dia", "emit-module-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-master.swiftdeps"}, "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController~partial.swiftmodule"}}