{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/SourceCode/iceAirPlay/build": {"is-mutated": true}, "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"is-mutated": true}, "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay": {"is-mutated": true}, "/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>": {"is-command-timestamp": true}, "<TRIGGER: CreateUniversalBinary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay normal arm64\\ x86_64>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/_CodeSignature", "/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<target-iceAirPlay-****************************************************************--begin-scanning>", "<target-iceAirPlay-****************************************************************--end>", "<target-iceAirPlay-****************************************************************--linker-inputs-ready>", "<target-iceAirPlay-****************************************************************--modules-ready>", "<workspace-Debug--stale-file-removal>"], "outputs": ["<all>"]}, "<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu/root.ssu.yaml", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/_CodeSignature", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Assets.car", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/PkgInfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap"], "roots": ["/tmp/iceAirPlay.dst", "/Users/<USER>/SourceCode/iceAirPlay/build", "/Users/<USER>/SourceCode/iceAirPlay/build"], "outputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay-a6cdb269d1ec068470896080ca0636af-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay.xcodeproj", "signature": "a36c16030631312f0fe828e7be2c0319"}, "P0:::CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "/Users/<USER>/SourceCode/iceAirPlay/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay-a6cdb269d1ec068470896080ca0636af-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList"], "outputs": ["<target-iceAirPlay-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-ChangePermissions>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-StripSymbols>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-GenerateStubAPI>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ProductPostprocessingTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-CodeSign>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-Validate>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-CopyAside>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--GeneratedFilesTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ProductStructureTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-iceAirPlay-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap"], "outputs": ["<target-iceAirPlay-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/PkgInfo"], "outputs": ["<target-iceAirPlay-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--RealityAssetsTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--ModuleMapTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-iceAirPlay-****************************************************************--InfoPlistTaskProducer>", "<target-iceAirPlay-****************************************************************--SanitizerTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-iceAirPlay-****************************************************************--TestTargetTaskProducer>", "<target-iceAirPlay-****************************************************************--TestHostTaskProducer>", "<target-iceAirPlay-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-iceAirPlay-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-iceAirPlay-****************************************************************--DocumentationTaskProducer>", "<target-iceAirPlay-****************************************************************--CustomTaskProducer>", "<target-iceAirPlay-****************************************************************--StubBinaryTaskProducer>", "<target-iceAirPlay-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--start>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--HeadermapTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<target-iceAirPlay-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ProductPostprocessingTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-iceAirPlay-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Assets.car", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Base.lproj/LaunchScreen.storyboardc/", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json"], "outputs": ["<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-iceAirPlay-****************************************************************--generated-headers>"]}, "P0:::Gate target-iceAirPlay-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h"], "outputs": ["<target-iceAirPlay-****************************************************************--swift-generated-headers>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "--temp-dir-path", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu", "--bundle-id", "com.iceairplay.app", "--product-path", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "--extracted-metadata-path", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents", "--metadata-file-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "signature": "bab8814eef1fa0fae8ad557f44976aa9"}, "P0:target-iceAirPlay-****************************************************************-:Debug:CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift/", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift/", "<target-iceAirPlay-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--entry>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay normal arm64\\ x86_64>", "<TRIGGER: MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/_CodeSignature", "<CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<TRIGGER: CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets/", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "--compile", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "14.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "control-enabled": false, "deps": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "032fb9d3b26a9083799345ed635653b4"}, "P0:target-iceAirPlay-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets/", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "--compile", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "14.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "control-enabled": false, "deps": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "d287694c6af51ecfb1b07b46029fcf3c"}, "P0:target-iceAirPlay-****************************************************************-:Debug:CompileStoryboard /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Base.lproj/LaunchScreen.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Base.lproj/LaunchScreen.storyboard", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Base.lproj/LaunchScreen.storyboard", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "iceAirPlay", "--output-partial-info-plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "14.0", "--output-format", "human-readable-text", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Base.lproj/LaunchScreen.storyboard", "--compilation-directory", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "control-enabled": false, "signature": "b13d27715c045fbd149be9418519bdbc"}, "P0:target-iceAirPlay-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "deps": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-iceAirPlay-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "iceAirPlay", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "14.0", "--bundle-identifier", "com.iceairplay.app", "--output", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "--target-triple", "arm64-apple-ios14.0-simulator", "--target-triple", "x86_64-apple-ios14.0-simulator", "--binary-file", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "--dependency-file", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "--dependency-file", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--stringsdata-file", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "--source-file-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "--metadata-file-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "--swift-const-vals-list", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "signature": "3acb401b1bcbec35c3be6c006c77bebd"}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/iceAirPlay.dst>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/iceAirPlay.dst>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-iceAirPlay-****************************************************************--begin-linking>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/iceAirPlay.dst>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--begin-scanning>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--end": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--entry>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "<CopySwiftStdlib /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<ExtractAppIntentsMetadata /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Assets.car", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Base.lproj/LaunchScreen.storyboardc/", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/PkgInfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<Validate /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap", "<target-iceAirPlay-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-iceAirPlay-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-iceAirPlay-****************************************************************--Barrier-ChangePermissions>", "<target-iceAirPlay-****************************************************************--Barrier-CodeSign>", "<target-iceAirPlay-****************************************************************--Barrier-CopyAside>", "<target-iceAirPlay-****************************************************************--Barrier-GenerateStubAPI>", "<target-iceAirPlay-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-iceAirPlay-****************************************************************--Barrier-RegisterProduct>", "<target-iceAirPlay-****************************************************************--Barrier-StripSymbols>", "<target-iceAirPlay-****************************************************************--Barrier-Validate>", "<target-iceAirPlay-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-iceAirPlay-****************************************************************--CustomTaskProducer>", "<target-iceAirPlay-****************************************************************--DocumentationTaskProducer>", "<target-iceAirPlay-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-iceAirPlay-****************************************************************--GeneratedFilesTaskProducer>", "<target-iceAirPlay-****************************************************************--HeadermapTaskProducer>", "<target-iceAirPlay-****************************************************************--InfoPlistTaskProducer>", "<target-iceAirPlay-****************************************************************--ModuleMapTaskProducer>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--ProductPostprocessingTaskProducer>", "<target-iceAirPlay-****************************************************************--ProductStructureTaskProducer>", "<target-iceAirPlay-****************************************************************--RealityAssetsTaskProducer>", "<target-iceAirPlay-****************************************************************--SanitizerTaskProducer>", "<target-iceAirPlay-****************************************************************--StubBinaryTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-iceAirPlay-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-iceAirPlay-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-iceAirPlay-****************************************************************--TestHostTaskProducer>", "<target-iceAirPlay-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-iceAirPlay-****************************************************************--TestTargetTaskProducer>", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-iceAirPlay-****************************************************************--generated-headers>", "<target-iceAirPlay-****************************************************************--swift-generated-headers>"], "outputs": ["<target-iceAirPlay-****************************************************************--end>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/iceAirPlay.dst>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-iceAirPlay-****************************************************************--begin-compiling>"], "outputs": ["<target-iceAirPlay-****************************************************************--entry>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************-Debug-iphonesimulator--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/iceAirPlay.dst>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-iceAirPlay-****************************************************************--immediate>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "<Linked Binary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList"], "outputs": ["<target-iceAirPlay-****************************************************************--linker-inputs-ready>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h"], "outputs": ["<target-iceAirPlay-****************************************************************--modules-ready>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--begin-compiling>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/ssu/root.ssu.yaml", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<ExtractAppIntentsMetadata /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Metadata.appintents>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Assets.car", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "<target-iceAirPlay-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-iceAirPlay-****************************************************************--unsigned-product-ready>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Gate target-iceAirPlay-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-iceAirPlay-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-iceAirPlay-****************************************************************--will-sign>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets/", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "--compile", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "14.0", "--platform", "iphonesimulator", "--bundle-identifier", "com.iceairplay.app", "--generate-swift-asset-symbol-extensions", "NO", "--generate-swift-asset-symbols", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "control-enabled": false, "signature": "f3093f13a7ef78a7d042fe93d233a0f1"}, "P0:target-iceAirPlay-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Assets.xcassets/", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned/", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Assets.car"], "deps": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_dependencies"}, "P0:target-iceAirPlay-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-iceAirPlay-****************************************************************-:Debug:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Base.lproj/LaunchScreen.storyboardc/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "iceAirPlay", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "14.0", "--output-format", "human-readable-text", "--link", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "control-enabled": false, "signature": "ab806785777e97cbd326b7e91f728b6b"}, "P0:target-iceAirPlay-****************************************************************-:Debug:MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["<target-iceAirPlay-****************************************************************--start>", "<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>", "<TRIGGER: MkDir /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/thinned>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "inputs": ["<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_output/unthinned>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist /Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Info.plist", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/Info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/assetcatalog_generated_info.plist", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/PkgInfo"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist", "<target-iceAirPlay-****************************************************************--ProductStructureTaskProducer>", "<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "<target-iceAirPlay-****************************************************************--ProductStructureTaskProducer>", "<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "-o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "signature": "4f712f72c7a9b51819402a1a2d70275a"}, "P0:target-iceAirPlay-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "<target-iceAirPlay-****************************************************************--Barrier-CodeSign>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:SwiftDriver Compilation iceAirPlay normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation iceAirPlay normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "<ClangStatCache /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-iceAirPlay-****************************************************************--generated-headers>", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:SwiftDriver Compilation iceAirPlay normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation iceAirPlay normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "<ClangStatCache /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-iceAirPlay-****************************************************************--generated-headers>", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-iceAirPlay-****************************************************************-:Debug:Touch /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "shell", "description": "Touch /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "<target-iceAirPlay-****************************************************************--Barrier-Validate>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "signature": "7a95c6163c3d4dce3922f6c7ef314e6b"}, "P0:target-iceAirPlay-****************************************************************-:Debug:Validate /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/Info.plist", "<target-iceAirPlay-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-iceAirPlay-****************************************************************--will-sign>", "<target-iceAirPlay-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"], "outputs": ["<Validate /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay-a6cdb269d1ec068470896080ca0636af-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay-a6cdb269d1ec068470896080ca0636af-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay-a6cdb269d1ec068470896080ca0636af-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.abi.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule/", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:CreateUniversalBinary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay normal arm64 x86_64": {"tool": "shell", "description": "CreateUniversalBinary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay normal arm64 x86_64", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay", "<Linked Binary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay normal arm64\\ x86_64>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo", "-create", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "-output", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator/iceAirPlay.app/iceAirPlay"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "signature": "edcdeba3e3b1a82deadd023bef0a425b"}, "P2:target-iceAirPlay-****************************************************************-:Debug:Ld /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay normal arm64": {"tool": "shell", "description": "Ld /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay normal arm64", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "<target-iceAirPlay-****************************************************************--generated-headers>", "<target-iceAirPlay-****************************************************************--swift-generated-headers>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios14.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "-F/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "-filelist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/Binary/iceAirPlay"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "deps": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_dependency_info.dat"], "deps-style": "dependency-info", "signature": "b351124b8d3cb3ac8a8b6c2986e12dab"}, "P2:target-iceAirPlay-****************************************************************-:Debug:Ld /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay normal x86_64": {"tool": "shell", "description": "Ld /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay normal x86_64", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AirPlayVideoPlayer.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "<target-iceAirPlay-****************************************************************--generated-headers>", "<target-iceAirPlay-****************************************************************--swift-generated-headers>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios14.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "-F/Users/<USER>/SourceCode/iceAirPlay/build/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/SourceCode/iceAirPlay/build/Debug-iphonesimulator", "-filelist", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/Binary/iceAirPlay"], "env": {}, "working-directory": "/Users/<USER>/SourceCode/iceAirPlay", "deps": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_dependency_info.dat"], "deps-style": "dependency-info", "signature": "a0b3b59360bdb7e8349be885c71ce2da"}, "P2:target-iceAirPlay-****************************************************************-:Debug:SwiftDriver Compilation Requirements iceAirPlay normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements iceAirPlay normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "<ClangStatCache /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.swiftdoc"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:SwiftDriver Compilation Requirements iceAirPlay normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements iceAirPlay normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "<ClangStatCache /var/folders/qy/016_5v5j1vlg7888xkv3d_q80000gq/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-iceAirPlay-****************************************************************--copy-headers-completion>", "<target-iceAirPlay-****************************************************************--ModuleVerifierTaskProducer>", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay Swift Compilation Requirements Finished", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftmodule", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftsourceinfo", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.abi.json", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.swiftdoc"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "inputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-Swift.h", "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-Swift.h", "<target-iceAirPlay-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/iceAirPlay-Swift.h"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-OutputFileMap.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.LinkFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftConstValuesFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay.SwiftFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay_const_extract_protocols.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay-OutputFileMap.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.LinkFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftConstValuesFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay.SwiftFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/x86_64/iceAirPlay_const_extract_protocols.json"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-non-framework-target-headers.hmap"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-all-target-headers.hmap"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-generated-files.hmap"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-own-target-headers.hmap"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay-project-headers.hmap"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyMetadataFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.DependencyStaticMetadataFileList"]}, "P2:target-iceAirPlay-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap", "inputs": ["<target-iceAirPlay-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/iceAirPlay.hmap"]}}}