{"": {"diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-master.dia", "emit-module-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/iceAirPlay-master.swiftdeps"}, "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AirPlayVideoPlayer.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AirPlayVideoPlayer~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/AppDelegate.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/AppDelegate~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/SceneDelegate.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/SceneDelegate~partial.swiftmodule"}, "/Users/<USER>/SourceCode/iceAirPlay/iceAirPlay/ViewController.swift": {"const-values": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftconstvalues", "dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.d", "diagnostics": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.dia", "index-unit-output-path": "/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "llvm-bc": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.bc", "object": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.o", "swift-dependencies": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController.swiftdeps", "swiftmodule": "/Users/<USER>/SourceCode/iceAirPlay/build/iceAirPlay.build/Debug-iphonesimulator/iceAirPlay.build/Objects-normal/arm64/ViewController~partial.swiftmodule"}}