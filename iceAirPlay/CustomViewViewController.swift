import UIKit

class CustomViewViewController: UIViewController {
    
    private var customViewPlayer: AirPlayCustomViewPlayer!
    private var segmentedControl: UISegmentedControl!
    private var instructionLabel: UILabel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // 创建标题
        title = "AirPlay Custom View Demo"
        
        // 创建说明标签
        instructionLabel = UILabel()
        instructionLabel.text = "1. 点击右上角AirPlay按钮连接设备\n2. 点击'Start AirPlay Mirroring'开始投屏\n3. 观察自定义内容的实时更新"
        instructionLabel.numberOfLines = 0
        instructionLabel.textAlignment = .center
        instructionLabel.font = UIFont.systemFont(ofSize: 14)
        instructionLabel.textColor = .secondaryLabel
        instructionLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(instructionLabel)
        
        // 创建自定义View播放器
        customViewPlayer = AirPlayCustomViewPlayer()
        customViewPlayer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(customViewPlayer)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 说明标签约束
            instructionLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            instructionLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            instructionLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            
            // 自定义View播放器约束
            customViewPlayer.topAnchor.constraint(equalTo: instructionLabel.bottomAnchor, constant: 20),
            customViewPlayer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            customViewPlayer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            customViewPlayer.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor)
        ])
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 显示使用提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.showWelcomeAlert()
        }
    }
    
    private func showWelcomeAlert() {
        let alert = UIAlertController(
            title: "🎯 AirPlay Custom View Demo",
            message: "这个演示展示了如何通过AirPlay投屏自定义UIView内容。\n\n步骤：\n1. 点击右上角的AirPlay按钮\n2. 选择目标设备（Apple TV等）\n3. 点击'Start AirPlay Mirroring'\n4. 观察内容的实时更新",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "开始体验", style: .default))
        present(alert, animated: true)
    }
}
