import UIKit
import AVFoundation
import AVKit

class AirPlayVideoPlayer: UIView {
    
    // MARK: - Properties
    private var player: AVPlayer?
    private var playerLayer: AVPlayerLayer?
    private var routePickerView: AVRoutePickerView?
    private var playButton: UIButton?
    private var pauseButton: UIButton?
    private var progressSlider: UISlider?
    private var timeObserver: Any?
    private var currentTimeLabel: UILabel?
    private var durationLabel: UILabel?
    private var volumeSlider: UISlider?
    private var muteButton: UIButton?
    private var fullscreenButton: UIButton?
    private var skipBackwardButton: UIButton?
    private var skipForwardButton: UIButton?
    private var isSliderBeingDragged = false
    private var isMuted = false
    private var previousVolume: Float = 1.0
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupPlayer()
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupPlayer()
        setupUI()
        setupConstraints()
    }
    
    deinit {
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
        }
    }
    
    // MARK: - Setup Methods
    private func setupPlayer() {
        // 创建AVPlayer
        player = AVPlayer()
        
        // 创建AVPlayerLayer
        playerLayer = AVPlayerLayer(player: player)
        playerLayer?.videoGravity = .resizeAspect
        layer.addSublayer(playerLayer!)
        
        // 启用外部播放（AirPlay）
        player?.allowsExternalPlayback = true
        player?.usesExternalPlaybackWhileExternalScreenIsActive = true
        
        // 添加时间观察者
        addTimeObserver()
    }
    
    private func setupUI() {
        backgroundColor = .black
        
        // 创建AirPlay路由选择器
        routePickerView = AVRoutePickerView()
        routePickerView?.backgroundColor = .clear
        routePickerView?.activeTintColor = .systemBlue
        routePickerView?.tintColor = .white
        addSubview(routePickerView!)
        
        // 创建播放按钮
        playButton = UIButton(type: .system)
        playButton?.setTitle("▶️", for: .normal)
        playButton?.titleLabel?.font = UIFont.systemFont(ofSize: 30)
        playButton?.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        addSubview(playButton!)
        
        // 创建暂停按钮
        pauseButton = UIButton(type: .system)
        pauseButton?.setTitle("⏸️", for: .normal)
        pauseButton?.titleLabel?.font = UIFont.systemFont(ofSize: 30)
        pauseButton?.addTarget(self, action: #selector(pauseButtonTapped), for: .touchUpInside)
        pauseButton?.isHidden = true
        addSubview(pauseButton!)
        
        // 创建进度滑块
        progressSlider = UISlider()
        progressSlider?.minimumValue = 0
        progressSlider?.maximumValue = 1
        progressSlider?.addTarget(self, action: #selector(sliderValueChanged), for: .valueChanged)
        progressSlider?.addTarget(self, action: #selector(sliderTouchBegan), for: .touchDown)
        progressSlider?.addTarget(self, action: #selector(sliderTouchEnded), for: [.touchUpInside, .touchUpOutside])
        addSubview(progressSlider!)
        
        // 创建时间标签
        currentTimeLabel = UILabel()
        currentTimeLabel?.text = "00:00"
        currentTimeLabel?.textColor = .white
        currentTimeLabel?.font = UIFont.systemFont(ofSize: 14)
        addSubview(currentTimeLabel!)
        
        durationLabel = UILabel()
        durationLabel?.text = "00:00"
        durationLabel?.textColor = .white
        durationLabel?.font = UIFont.systemFont(ofSize: 14)
        addSubview(durationLabel!)

        // 创建音量滑块
        volumeSlider = UISlider()
        volumeSlider?.minimumValue = 0
        volumeSlider?.maximumValue = 1
        volumeSlider?.value = 1.0
        volumeSlider?.addTarget(self, action: #selector(volumeChanged), for: .valueChanged)
        addSubview(volumeSlider!)

        // 创建静音按钮
        muteButton = UIButton(type: .system)
        muteButton?.setTitle("🔊", for: .normal)
        muteButton?.titleLabel?.font = UIFont.systemFont(ofSize: 20)
        muteButton?.addTarget(self, action: #selector(muteButtonTapped), for: .touchUpInside)
        addSubview(muteButton!)

        // 创建全屏按钮
        fullscreenButton = UIButton(type: .system)
        fullscreenButton?.setTitle("⛶", for: .normal)
        fullscreenButton?.titleLabel?.font = UIFont.systemFont(ofSize: 20)
        fullscreenButton?.addTarget(self, action: #selector(fullscreenButtonTapped), for: .touchUpInside)
        addSubview(fullscreenButton!)

        // 创建快退按钮
        skipBackwardButton = UIButton(type: .system)
        skipBackwardButton?.setTitle("⏪", for: .normal)
        skipBackwardButton?.titleLabel?.font = UIFont.systemFont(ofSize: 25)
        skipBackwardButton?.addTarget(self, action: #selector(skipBackward), for: .touchUpInside)
        addSubview(skipBackwardButton!)

        // 创建快进按钮
        skipForwardButton = UIButton(type: .system)
        skipForwardButton?.setTitle("⏩", for: .normal)
        skipForwardButton?.titleLabel?.font = UIFont.systemFont(ofSize: 25)
        skipForwardButton?.addTarget(self, action: #selector(skipForward), for: .touchUpInside)
        addSubview(skipForwardButton!)
    }
    
    private func setupConstraints() {
        routePickerView?.translatesAutoresizingMaskIntoConstraints = false
        playButton?.translatesAutoresizingMaskIntoConstraints = false
        pauseButton?.translatesAutoresizingMaskIntoConstraints = false
        progressSlider?.translatesAutoresizingMaskIntoConstraints = false
        currentTimeLabel?.translatesAutoresizingMaskIntoConstraints = false
        durationLabel?.translatesAutoresizingMaskIntoConstraints = false
        volumeSlider?.translatesAutoresizingMaskIntoConstraints = false
        muteButton?.translatesAutoresizingMaskIntoConstraints = false
        fullscreenButton?.translatesAutoresizingMaskIntoConstraints = false
        skipBackwardButton?.translatesAutoresizingMaskIntoConstraints = false
        skipForwardButton?.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // AirPlay按钮约束
            routePickerView!.topAnchor.constraint(equalTo: safeAreaLayoutGuide.topAnchor, constant: 20),
            routePickerView!.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            routePickerView!.widthAnchor.constraint(equalToConstant: 44),
            routePickerView!.heightAnchor.constraint(equalToConstant: 44),
            
            // 播放按钮约束
            playButton!.centerXAnchor.constraint(equalTo: centerXAnchor),
            playButton!.centerYAnchor.constraint(equalTo: centerYAnchor),
            playButton!.widthAnchor.constraint(equalToConstant: 60),
            playButton!.heightAnchor.constraint(equalToConstant: 60),
            
            // 暂停按钮约束
            pauseButton!.centerXAnchor.constraint(equalTo: centerXAnchor),
            pauseButton!.centerYAnchor.constraint(equalTo: centerYAnchor),
            pauseButton!.widthAnchor.constraint(equalToConstant: 60),
            pauseButton!.heightAnchor.constraint(equalToConstant: 60),
            
            // 当前时间标签约束
            currentTimeLabel!.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            currentTimeLabel!.bottomAnchor.constraint(equalTo: safeAreaLayoutGuide.bottomAnchor, constant: -20),
            
            // 持续时间标签约束
            durationLabel!.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            durationLabel!.bottomAnchor.constraint(equalTo: safeAreaLayoutGuide.bottomAnchor, constant: -20),
            
            // 进度滑块约束
            progressSlider!.leadingAnchor.constraint(equalTo: currentTimeLabel!.trailingAnchor, constant: 10),
            progressSlider!.trailingAnchor.constraint(equalTo: durationLabel!.leadingAnchor, constant: -10),
            progressSlider!.centerYAnchor.constraint(equalTo: currentTimeLabel!.centerYAnchor),

            // 快退按钮约束
            skipBackwardButton!.trailingAnchor.constraint(equalTo: playButton!.leadingAnchor, constant: -30),
            skipBackwardButton!.centerYAnchor.constraint(equalTo: playButton!.centerYAnchor),
            skipBackwardButton!.widthAnchor.constraint(equalToConstant: 50),
            skipBackwardButton!.heightAnchor.constraint(equalToConstant: 50),

            // 快进按钮约束
            skipForwardButton!.leadingAnchor.constraint(equalTo: playButton!.trailingAnchor, constant: 30),
            skipForwardButton!.centerYAnchor.constraint(equalTo: playButton!.centerYAnchor),
            skipForwardButton!.widthAnchor.constraint(equalToConstant: 50),
            skipForwardButton!.heightAnchor.constraint(equalToConstant: 50),

            // 静音按钮约束
            muteButton!.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            muteButton!.topAnchor.constraint(equalTo: safeAreaLayoutGuide.topAnchor, constant: 20),
            muteButton!.widthAnchor.constraint(equalToConstant: 44),
            muteButton!.heightAnchor.constraint(equalToConstant: 44),

            // 音量滑块约束
            volumeSlider!.leadingAnchor.constraint(equalTo: muteButton!.trailingAnchor, constant: 10),
            volumeSlider!.centerYAnchor.constraint(equalTo: muteButton!.centerYAnchor),
            volumeSlider!.widthAnchor.constraint(equalToConstant: 100),

            // 全屏按钮约束
            fullscreenButton!.trailingAnchor.constraint(equalTo: routePickerView!.leadingAnchor, constant: -10),
            fullscreenButton!.centerYAnchor.constraint(equalTo: routePickerView!.centerYAnchor),
            fullscreenButton!.widthAnchor.constraint(equalToConstant: 44),
            fullscreenButton!.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer?.frame = bounds
    }
    
    // MARK: - Public Methods
    func loadVideo(from url: URL) {
        let playerItem = AVPlayerItem(url: url)
        player?.replaceCurrentItem(with: playerItem)
        
        // 监听播放状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: playerItem
        )
    }
    
    func play() {
        player?.play()
        playButton?.isHidden = true
        pauseButton?.isHidden = false
    }
    
    func pause() {
        player?.pause()
        playButton?.isHidden = false
        pauseButton?.isHidden = true
    }
    
    // MARK: - Private Methods
    private func addTimeObserver() {
        let interval = CMTime(seconds: 1.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player?.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.updateProgress()
        }
    }
    
    private func updateProgress() {
        guard let player = player,
              let currentItem = player.currentItem,
              !isSliderBeingDragged else { return }

        let currentTime = player.currentTime()
        let duration = currentItem.duration

        if duration.isValid && !duration.isIndefinite {
            let currentSeconds = CMTimeGetSeconds(currentTime)
            let durationSeconds = CMTimeGetSeconds(duration)

            progressSlider?.value = Float(currentSeconds / durationSeconds)
            currentTimeLabel?.text = formatTime(currentSeconds)
            durationLabel?.text = formatTime(durationSeconds)
        }
    }
    
    private func formatTime(_ seconds: Double) -> String {
        let minutes = Int(seconds) / 60
        let remainingSeconds = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    // MARK: - Action Methods
    @objc private func playButtonTapped() {
        play()
    }
    
    @objc private func pauseButtonTapped() {
        pause()
    }
    
    @objc private func sliderValueChanged() {
        guard let player = player,
              let currentItem = player.currentItem,
              let slider = progressSlider else { return }
        
        let duration = currentItem.duration
        if duration.isValid && !duration.isIndefinite {
            let durationSeconds = CMTimeGetSeconds(duration)
            let seekTime = CMTime(seconds: Double(slider.value) * durationSeconds, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
            player.seek(to: seekTime)
        }
    }
    
    @objc private func sliderTouchBegan() {
        // 暂停时间观察者，避免在拖拽时更新
        isSliderBeingDragged = true
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
        }
    }

    @objc private func sliderTouchEnded() {
        // 重新添加时间观察者
        isSliderBeingDragged = false
        addTimeObserver()
    }
    
    @objc private func playerDidFinishPlaying() {
        playButton?.isHidden = false
        pauseButton?.isHidden = true
        progressSlider?.value = 0
    }

    @objc private func volumeChanged() {
        guard let slider = volumeSlider else { return }
        player?.volume = slider.value

        // 更新静音按钮状态
        if slider.value == 0 {
            muteButton?.setTitle("🔇", for: .normal)
            isMuted = true
        } else {
            muteButton?.setTitle("🔊", for: .normal)
            isMuted = false
        }
    }

    @objc private func muteButtonTapped() {
        guard let volumeSlider = volumeSlider else { return }

        if isMuted {
            // 取消静音
            volumeSlider.value = previousVolume
            player?.volume = previousVolume
            muteButton?.setTitle("🔊", for: .normal)
            isMuted = false
        } else {
            // 静音
            previousVolume = volumeSlider.value
            volumeSlider.value = 0
            player?.volume = 0
            muteButton?.setTitle("🔇", for: .normal)
            isMuted = true
        }
    }

    @objc private func fullscreenButtonTapped() {
        // 这里可以实现全屏功能
        // 由于我们使用的是自定义播放器，可以通过修改约束来实现全屏
        print("全屏功能待实现")
    }

    @objc private func skipBackward() {
        guard let player = player else { return }
        let currentTime = player.currentTime()
        let newTime = CMTimeSubtract(currentTime, CMTime(seconds: 10, preferredTimescale: 1))
        let seekTime = CMTimeMaximum(newTime, CMTime.zero)
        player.seek(to: seekTime)
    }

    @objc private func skipForward() {
        guard let player = player,
              let currentItem = player.currentItem else { return }

        let currentTime = player.currentTime()
        let newTime = CMTimeAdd(currentTime, CMTime(seconds: 10, preferredTimescale: 1))
        let duration = currentItem.duration

        if duration.isValid && !duration.isIndefinite {
            let seekTime = CMTimeMinimum(newTime, duration)
            player.seek(to: seekTime)
        } else {
            player.seek(to: newTime)
        }
    }
}
