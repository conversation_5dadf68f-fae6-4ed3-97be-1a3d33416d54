import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        window = UIWindow(windowScene: windowScene)

        // 创建Tab Bar Controller来展示两种AirPlay功能
        let tabBarController = UITabBarController()

        // 视频播放器标签
        let videoViewController = ViewController()
        videoViewController.title = "Video Player"
        videoViewController.tabBarItem = UITabBarItem(title: "Video AirPlay", image: UIImage(systemName: "play.rectangle"), tag: 0)
        let videoNavController = UINavigationController(rootViewController: videoViewController)

        // 自定义View投屏标签
        let customViewController = CustomViewViewController()
        customViewController.title = "Custom View"
        customViewController.tabBarItem = UITabBarItem(title: "Custom View", image: UIImage(systemName: "rectangle.on.rectangle"), tag: 1)
        let customNavController = UINavigationController(rootViewController: customViewController)

        tabBarController.viewControllers = [videoNavController, customNavController]
        tabBarController.selectedIndex = 1 // 默认显示自定义View标签

        window?.rootViewController = tabBarController
        window?.makeKeyAndVisible()
    }

    func sceneDidDisconnect(_ scene: UIScene) {
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
    }

    func sceneWillResignActive(_ scene: UIScene) {
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
    }
}
