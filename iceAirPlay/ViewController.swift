import UIKit
import AVFoundation

class ViewController: UIViewController {
    
    // MARK: - Properties
    private var videoPlayer: AirPlayVideoPlayer!
    private var loadVideoButton: UIButton!
    private var videoURLTextField: UITextField!
    private var statusLabel: UILabel!
    
    // 示例视频URL
    private let sampleVideoURLs = [
        "http://www.w3school.com.cn/i/movie.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4"
    ]
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadDefaultVideo()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保音频会话配置正确
        configureAudioSession()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // 创建视频播放器
        videoPlayer = AirPlayVideoPlayer()
        videoPlayer.backgroundColor = .black
        videoPlayer.layer.cornerRadius = 12
        videoPlayer.clipsToBounds = true
        view.addSubview(videoPlayer)
        
        // 创建URL输入框
        videoURLTextField = UITextField()
        videoURLTextField.borderStyle = .roundedRect
        videoURLTextField.placeholder = "输入视频URL或使用默认视频"
        videoURLTextField.text = sampleVideoURLs[0]
        videoURLTextField.font = UIFont.systemFont(ofSize: 16)
        view.addSubview(videoURLTextField)
        
        // 创建加载视频按钮
        loadVideoButton = UIButton(type: .system)
        loadVideoButton.setTitle("加载视频", for: .normal)
        loadVideoButton.backgroundColor = .systemBlue
        loadVideoButton.setTitleColor(.white, for: .normal)
        loadVideoButton.layer.cornerRadius = 8
        loadVideoButton.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        loadVideoButton.addTarget(self, action: #selector(loadVideoButtonTapped), for: .touchUpInside)
        view.addSubview(loadVideoButton)
        
        // 创建状态标签
        statusLabel = UILabel()
        statusLabel.text = "准备播放视频"
        statusLabel.textAlignment = .center
        statusLabel.font = UIFont.systemFont(ofSize: 14)
        statusLabel.textColor = .secondaryLabel
        statusLabel.numberOfLines = 0
        view.addSubview(statusLabel)
        
        // 添加示例视频按钮
        createSampleVideoButtons()
    }
    
    private func createSampleVideoButtons() {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 10
        
        for (index, _) in sampleVideoURLs.enumerated() {
            let button = UIButton(type: .system)
            button.setTitle("示例\(index + 1)", for: .normal)
            button.backgroundColor = .systemGray5
            button.setTitleColor(.label, for: .normal)
            button.layer.cornerRadius = 6
            button.tag = index
            button.addTarget(self, action: #selector(sampleVideoButtonTapped(_:)), for: .touchUpInside)
            stackView.addArrangedSubview(button)
        }
        
        view.addSubview(stackView)
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: view.safeAreaLayoutGuide.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            stackView.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func setupConstraints() {
        videoPlayer.translatesAutoresizingMaskIntoConstraints = false
        videoURLTextField.translatesAutoresizingMaskIntoConstraints = false
        loadVideoButton.translatesAutoresizingMaskIntoConstraints = false
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            // 视频播放器约束
            videoPlayer.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            videoPlayer.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoPlayer.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoPlayer.heightAnchor.constraint(equalTo: videoPlayer.widthAnchor, multiplier: 9.0/16.0),
            
            // URL输入框约束
            videoURLTextField.topAnchor.constraint(equalTo: videoPlayer.bottomAnchor, constant: 20),
            videoURLTextField.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoURLTextField.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoURLTextField.heightAnchor.constraint(equalToConstant: 44),
            
            // 加载按钮约束
            loadVideoButton.topAnchor.constraint(equalTo: videoURLTextField.bottomAnchor, constant: 15),
            loadVideoButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadVideoButton.widthAnchor.constraint(equalToConstant: 120),
            loadVideoButton.heightAnchor.constraint(equalToConstant: 44),
            
            // 状态标签约束
            statusLabel.topAnchor.constraint(equalTo: loadVideoButton.bottomAnchor, constant: 20),
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20)
        ])
    }
    
    private func configureAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback, mode: .moviePlayback, options: [.allowAirPlay, .allowBluetooth, .allowBluetoothA2DP])
            try audioSession.setActive(true)
        } catch {
            print("Failed to configure audio session: \(error)")
            updateStatus("音频会话配置失败: \(error.localizedDescription)")
        }
    }
    
    private func loadDefaultVideo() {
        if let url = URL(string: sampleVideoURLs[0]) {
            loadVideo(from: url)
        }
    }
    
    private func loadVideo(from url: URL) {
        updateStatus("正在加载视频...")
        videoPlayer.loadVideo(from: url)
        updateStatus("视频已加载，点击播放按钮开始播放\n使用右上角的AirPlay按钮投屏到其他设备")
    }
    
    private func updateStatus(_ message: String) {
        DispatchQueue.main.async {
            self.statusLabel.text = message
        }
    }
    
    // MARK: - Action Methods
    @objc private func loadVideoButtonTapped() {
        guard let urlString = videoURLTextField.text,
              !urlString.isEmpty,
              let url = URL(string: urlString) else {
            updateStatus("请输入有效的视频URL")
            return
        }
        
        loadVideo(from: url)
    }
    
    @objc private func sampleVideoButtonTapped(_ sender: UIButton) {
        let urlString = sampleVideoURLs[sender.tag]
        videoURLTextField.text = urlString
        
        if let url = URL(string: urlString) {
            loadVideo(from: url)
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        view.endEditing(true)
    }
}
