# iceAirPlay - iOS AirPlay Demo App

这是一个演示iOS AirPlay功能的应用程序，支持两种不同的投屏方式：

## 功能特性

### 1. 视频播放器 AirPlay (Video Player Tab)
- 基于 AVPlayer 的视频播放功能
- 支持通过 AirPlay 投屏视频内容到 Apple TV 等设备
- 包含完整的媒体控制界面
- 支持多种视频格式和网络视频流

### 2. 自定义视图 AirPlay (Custom View Tab) ⭐️ 新功能
- **不使用 AVPlayer**，而是投屏自定义的 UIView 内容
- 支持动态更新显示内容
- 实时内容同步到外部显示设备
- 包含多种演示内容：
  - 动态颜色变化
  - 文本内容更新
  - 计数器动画
  - 进度条显示
  - 浮动动画效果

## 技术实现

### 自定义视图投屏技术栈
- `UIScreen` API 用于检测外部显示设备
- `UIWindow` 创建外部显示窗口
- `AVRoutePickerView` 提供 AirPlay 设备选择界面
- `NotificationCenter` 监听屏幕连接/断开事件
- `Timer` 实现内容的实时更新

### 核心类
- `AirPlayCustomViewPlayer`: 自定义视图投屏的核心实现
- `CustomViewViewController`: 演示界面控制器
- `AirPlayVideoPlayer`: 传统视频播放器实现

## 使用方法

1. **启动应用**：应用会显示两个标签页
2. **选择功能**：
   - "Video AirPlay" 标签：传统视频投屏
   - "Custom View" 标签：自定义视图投屏（默认选中）
3. **连接设备**：点击右上角的 AirPlay 按钮选择目标设备
4. **开始投屏**：
   - 自定义视图：点击 "Start AirPlay Mirroring" 开始投屏
   - 视频播放：直接播放视频即可投屏
5. **观察效果**：在外部设备上观察内容的实时更新

## 项目结构

```
iceAirPlay/
├── AppDelegate.swift              # 应用程序委托
├── SceneDelegate.swift            # 场景委托（支持 Tab Bar）
├── ViewController.swift           # 视频播放器界面
├── CustomViewViewController.swift # 自定义视图界面
├── AirPlayVideoPlayer.swift       # 视频播放器实现
├── AirPlayCustomViewPlayer.swift  # 自定义视图投屏实现
├── Assets.xcassets               # 资源文件
├── LaunchScreen.storyboard       # 启动屏幕
└── Info.plist                    # 应用配置
```

## 系统要求

- iOS 14.0+
- Xcode 14.0+
- Swift 5.0+
- 支持 AirPlay 的外部设备（Apple TV、兼容显示器等）

## 特色功能

### 动态内容更新
自定义视图投屏支持以下动态内容：
- ✅ 背景颜色循环变化
- ✅ 文本内容定期更新
- ✅ 数字计数器递增
- ✅ 进度条动画
- ✅ 浮动缩放动画
- ✅ 实时同步到外部显示

### 用户交互
- 开始/停止投屏控制
- 手动更新内容
- 动画开关控制
- AirPlay 设备选择

## 开发说明

这个项目展示了如何在不使用 AVPlayer 的情况下，通过 AirPlay 投屏自定义的 UIView 内容。这对于需要投屏应用界面、图表、演示文稿或其他非视频内容的场景非常有用。

核心技术点：
1. 使用 `UIScreen.screens` 检测外部显示设备
2. 创建 `UIWindow` 并设置到外部屏幕
3. 实时更新外部显示的内容
4. 处理设备连接/断开事件
