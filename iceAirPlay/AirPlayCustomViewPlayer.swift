import UIKit
import AVFoundation
import AVKit

class AirPlayCustomViewPlayer: UIView {
    
    // MARK: - Properties
    private var externalWindow: UIWindow?
    private var externalViewController: UIViewController?
    private var contentView: UIView!
    private var routePickerView: AVRoutePickerView?
    
    // Demo content properties
    private var displayTimer: Timer?
    private var currentColorIndex = 0
    private var currentTextIndex = 0
    
    // Demo colors and texts
    private let demoColors: [UIColor] = [
        .systemRed, .systemBlue, .systemGreen, .systemOrange, 
        .systemPurple, .systemYellow, .systemPink, .systemTeal
    ]
    
    private let demoTexts = [
        "Hello AirPlay! 🎉",
        "Custom View Mirroring 📱",
        "Dynamic Content 🔄",
        "Real-time Updates ⚡",
        "iOS Development 💻",
        "Swift Programming 🚀"
    ]
    
    // UI Elements for the custom content
    private var backgroundColorView: UIView!
    private var titleLabel: UILabel!
    private var subtitleLabel: UILabel!
    private var animatedView: UIView!
    private var counterLabel: UILabel!
    private var progressView: UIProgressView!
    
    // Control UI elements
    private var controlsContainerView: UIView!
    private var startMirroringButton: UIButton!
    private var stopMirroringButton: UIButton!
    private var updateContentButton: UIButton!
    private var animationSwitch: UISwitch!
    private var animationLabel: UILabel!
    
    private var counter = 0
    private var progress: Float = 0.0
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupNotifications()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupNotifications()
    }
    
    deinit {
        stopMirroring()
        displayTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .systemBackground
        
        // Setup AirPlay Route Picker
        setupRoutePickerView()
        
        // Setup custom content view
        setupContentView()
        
        // Setup Controls
        setupControlsUI()
        
        // Setup constraints
        setupConstraints()
        
        // Start demo content updates
        startContentUpdates()
    }
    
    private func setupRoutePickerView() {
        routePickerView = AVRoutePickerView()
        routePickerView?.backgroundColor = .clear
        routePickerView?.activeTintColor = .systemBlue
        routePickerView?.translatesAutoresizingMaskIntoConstraints = false
        addSubview(routePickerView!)
    }
    
    private func setupContentView() {
        // Main content view that will be mirrored
        contentView = UIView()
        contentView.backgroundColor = .systemBlue
        contentView.layer.cornerRadius = 15
        contentView.layer.shadowColor = UIColor.black.cgColor
        contentView.layer.shadowOffset = CGSize(width: 0, height: 4)
        contentView.layer.shadowRadius = 8
        contentView.layer.shadowOpacity = 0.3
        contentView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(contentView)
        
        // Background color view for animations
        backgroundColorView = UIView()
        backgroundColorView.backgroundColor = demoColors[0]
        backgroundColorView.layer.cornerRadius = 15
        backgroundColorView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(backgroundColorView)
        
        // Title label
        titleLabel = UILabel()
        titleLabel.text = demoTexts[0]
        titleLabel.textColor = .white
        titleLabel.font = UIFont.boldSystemFont(ofSize: 28)
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(titleLabel)
        
        // Subtitle label
        subtitleLabel = UILabel()
        subtitleLabel.text = "Tap 'Update Content' to see changes"
        subtitleLabel.textColor = .white.withAlphaComponent(0.8)
        subtitleLabel.font = UIFont.systemFont(ofSize: 16)
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(subtitleLabel)
        
        // Animated view
        animatedView = UIView()
        animatedView.backgroundColor = .white.withAlphaComponent(0.3)
        animatedView.layer.cornerRadius = 25
        animatedView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(animatedView)
        
        // Counter label
        counterLabel = UILabel()
        counterLabel.text = "Counter: 0"
        counterLabel.textColor = .white
        counterLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 20, weight: .medium)
        counterLabel.textAlignment = .center
        counterLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(counterLabel)
        
        // Progress view
        progressView = UIProgressView(progressViewStyle: .default)
        progressView.progressTintColor = .white
        progressView.trackTintColor = .white.withAlphaComponent(0.3)
        progressView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(progressView)
    }
    
    private func setupControlsUI() {
        // Controls container
        controlsContainerView = UIView()
        controlsContainerView.backgroundColor = .secondarySystemBackground
        controlsContainerView.layer.cornerRadius = 15
        controlsContainerView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(controlsContainerView)
        
        // Start mirroring button
        startMirroringButton = UIButton(type: .system)
        startMirroringButton.setTitle("🎯 Start AirPlay Mirroring", for: .normal)
        startMirroringButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        startMirroringButton.backgroundColor = .systemGreen
        startMirroringButton.setTitleColor(.white, for: .normal)
        startMirroringButton.layer.cornerRadius = 8
        startMirroringButton.addTarget(self, action: #selector(startMirroringTapped), for: .touchUpInside)
        startMirroringButton.translatesAutoresizingMaskIntoConstraints = false
        controlsContainerView.addSubview(startMirroringButton)
        
        // Stop mirroring button
        stopMirroringButton = UIButton(type: .system)
        stopMirroringButton.setTitle("⏹️ Stop Mirroring", for: .normal)
        stopMirroringButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        stopMirroringButton.backgroundColor = .systemRed
        stopMirroringButton.setTitleColor(.white, for: .normal)
        stopMirroringButton.layer.cornerRadius = 8
        stopMirroringButton.addTarget(self, action: #selector(stopMirroringTapped), for: .touchUpInside)
        stopMirroringButton.isEnabled = false
        stopMirroringButton.alpha = 0.5
        stopMirroringButton.translatesAutoresizingMaskIntoConstraints = false
        controlsContainerView.addSubview(stopMirroringButton)
        
        // Update content button
        updateContentButton = UIButton(type: .system)
        updateContentButton.setTitle("🔄 Update Content", for: .normal)
        updateContentButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        updateContentButton.backgroundColor = .systemBlue
        updateContentButton.setTitleColor(.white, for: .normal)
        updateContentButton.layer.cornerRadius = 8
        updateContentButton.addTarget(self, action: #selector(updateContentTapped), for: .touchUpInside)
        updateContentButton.translatesAutoresizingMaskIntoConstraints = false
        controlsContainerView.addSubview(updateContentButton)
        
        // Animation switch and label
        animationLabel = UILabel()
        animationLabel.text = "Auto Animation:"
        animationLabel.font = UIFont.systemFont(ofSize: 16)
        animationLabel.translatesAutoresizingMaskIntoConstraints = false
        controlsContainerView.addSubview(animationLabel)
        
        animationSwitch = UISwitch()
        animationSwitch.isOn = true
        animationSwitch.addTarget(self, action: #selector(animationSwitchChanged), for: .valueChanged)
        animationSwitch.translatesAutoresizingMaskIntoConstraints = false
        controlsContainerView.addSubview(animationSwitch)
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // AirPlay Route Picker
            routePickerView!.topAnchor.constraint(equalTo: safeAreaLayoutGuide.topAnchor, constant: 20),
            routePickerView!.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            routePickerView!.widthAnchor.constraint(equalToConstant: 44),
            routePickerView!.heightAnchor.constraint(equalToConstant: 44),

            // Content view (the view that will be mirrored)
            contentView.topAnchor.constraint(equalTo: safeAreaLayoutGuide.topAnchor, constant: 80),
            contentView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            contentView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            contentView.heightAnchor.constraint(equalToConstant: 300),

            // Background color view
            backgroundColorView.topAnchor.constraint(equalTo: contentView.topAnchor),
            backgroundColorView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            backgroundColorView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            backgroundColorView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // Title label
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 30),
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Subtitle label
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
            subtitleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            subtitleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Animated view
            animatedView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            animatedView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            animatedView.widthAnchor.constraint(equalToConstant: 50),
            animatedView.heightAnchor.constraint(equalToConstant: 50),

            // Counter label
            counterLabel.topAnchor.constraint(equalTo: animatedView.bottomAnchor, constant: 20),
            counterLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            counterLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Progress view
            progressView.topAnchor.constraint(equalTo: counterLabel.bottomAnchor, constant: 15),
            progressView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            progressView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),

            // Controls container
            controlsContainerView.topAnchor.constraint(equalTo: contentView.bottomAnchor, constant: 30),
            controlsContainerView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
            controlsContainerView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
            controlsContainerView.bottomAnchor.constraint(lessThanOrEqualTo: safeAreaLayoutGuide.bottomAnchor, constant: -20),
            controlsContainerView.heightAnchor.constraint(equalToConstant: 180),

            // Start mirroring button
            startMirroringButton.topAnchor.constraint(equalTo: controlsContainerView.topAnchor, constant: 15),
            startMirroringButton.leadingAnchor.constraint(equalTo: controlsContainerView.leadingAnchor, constant: 15),
            startMirroringButton.trailingAnchor.constraint(equalTo: controlsContainerView.trailingAnchor, constant: -15),
            startMirroringButton.heightAnchor.constraint(equalToConstant: 44),

            // Stop mirroring button
            stopMirroringButton.topAnchor.constraint(equalTo: startMirroringButton.bottomAnchor, constant: 10),
            stopMirroringButton.leadingAnchor.constraint(equalTo: controlsContainerView.leadingAnchor, constant: 15),
            stopMirroringButton.trailingAnchor.constraint(equalTo: controlsContainerView.trailingAnchor, constant: -15),
            stopMirroringButton.heightAnchor.constraint(equalToConstant: 44),

            // Update content button
            updateContentButton.topAnchor.constraint(equalTo: stopMirroringButton.bottomAnchor, constant: 10),
            updateContentButton.leadingAnchor.constraint(equalTo: controlsContainerView.leadingAnchor, constant: 15),
            updateContentButton.trailingAnchor.constraint(equalTo: controlsContainerView.trailingAnchor, constant: -15),
            updateContentButton.heightAnchor.constraint(equalToConstant: 44),

            // Animation controls
            animationLabel.topAnchor.constraint(equalTo: updateContentButton.bottomAnchor, constant: 15),
            animationLabel.leadingAnchor.constraint(equalTo: controlsContainerView.leadingAnchor, constant: 15),

            animationSwitch.centerYAnchor.constraint(equalTo: animationLabel.centerYAnchor),
            animationSwitch.trailingAnchor.constraint(equalTo: controlsContainerView.trailingAnchor, constant: -15)
        ])
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenDidConnect),
            name: UIScreen.didConnectNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenDidDisconnect),
            name: UIScreen.didDisconnectNotification,
            object: nil
        )
    }

    // MARK: - Content Updates
    private func startContentUpdates() {
        displayTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            guard let self = self, self.animationSwitch.isOn else { return }
            self.updateContent()
        }
    }

    private func updateContent() {
        // Update counter
        counter += 1
        counterLabel.text = "Counter: \(counter)"

        // Update progress
        progress += 0.1
        if progress > 1.0 { progress = 0.0 }
        progressView.setProgress(progress, animated: true)

        // Animate color change
        currentColorIndex = (currentColorIndex + 1) % demoColors.count
        UIView.animate(withDuration: 0.5) {
            self.backgroundColorView.backgroundColor = self.demoColors[self.currentColorIndex]
        }

        // Animate the floating view
        UIView.animate(withDuration: 1.0, delay: 0, options: [.repeat, .autoreverse], animations: {
            self.animatedView.transform = CGAffineTransform(scaleX: 1.5, y: 1.5)
        }) { _ in
            self.animatedView.transform = .identity
        }

        // Update text occasionally
        if counter % 3 == 0 {
            currentTextIndex = (currentTextIndex + 1) % demoTexts.count
            titleLabel.text = demoTexts[currentTextIndex]
        }
    }

    // MARK: - AirPlay Mirroring Methods
    @objc private func startMirroringTapped() {
        startMirroring()
    }

    @objc private func stopMirroringTapped() {
        stopMirroring()
    }

    @objc private func updateContentTapped() {
        updateContent()
    }

    @objc private func animationSwitchChanged() {
        if animationSwitch.isOn {
            startContentUpdates()
        } else {
            displayTimer?.invalidate()
            displayTimer = nil
        }
    }

    private func startMirroring() {
        // Check if external screen is available
        guard UIScreen.screens.count > 1 else {
            showAlert(title: "No External Display", message: "Please connect to an AirPlay device first using the AirPlay button.")
            return
        }
        let scene = UIApplication.shared.connectedScenes
        let session = UIApplication.shared.openSessions
        
        let exter = scene.first;
        
        
        
        let externalScreen = UIScreen.screens[1]

        // Create external window
        externalWindow = UIWindow(frame: externalScreen.bounds)
//        externalWindow?.screen = externalScreen
        externalWindow?.windowScene = exter as? UIWindowScene;

        // Create view controller for external display
        externalViewController = UIViewController()
        externalWindow?.rootViewController = externalViewController
        externalViewController?.view.backgroundColor = .yellow
        // Create a copy of our content view for the external display
        let mirroredContentView = createMirroredContentView()
//        externalViewController?.view.addSubview(mirroredContentView)
        mirroredContentView.backgroundColor = .yellow
        // Setup constraints for the mirrored view
        mirroredContentView.translatesAutoresizingMaskIntoConstraints = false
//        NSLayoutConstraint.activate([
//            mirroredContentView.centerXAnchor.constraint(equalTo: externalViewController!.view.centerXAnchor),
//            mirroredContentView.centerYAnchor.constraint(equalTo: externalViewController!.view.centerYAnchor),
//            mirroredContentView.widthAnchor.constraint(equalToConstant: 600),
//            mirroredContentView.heightAnchor.constraint(equalToConstant: 400)
//        ])

        // Show the external window
        externalWindow?.isHidden = false

        // Update UI
        startMirroringButton.isEnabled = false
        startMirroringButton.alpha = 0.5
        stopMirroringButton.isEnabled = true
        stopMirroringButton.alpha = 1.0

        print("Started mirroring to external display")
    }

    private func stopMirroring() {
        externalWindow?.isHidden = true
        externalWindow = nil
        externalViewController = nil

        // Update UI
        startMirroringButton.isEnabled = true
        startMirroringButton.alpha = 1.0
        stopMirroringButton.isEnabled = false
        stopMirroringButton.alpha = 0.5

        print("Stopped mirroring")
    }

    private func createMirroredContentView() -> UIView {
        let mirroredView = UIView()
        mirroredView.backgroundColor = backgroundColorView.backgroundColor
        mirroredView.layer.cornerRadius = 15

        // Create mirrored title label
        let mirroredTitleLabel = UILabel()
        mirroredTitleLabel.text = titleLabel.text
        mirroredTitleLabel.textColor = .white
        mirroredTitleLabel.font = UIFont.boldSystemFont(ofSize: 48)
        mirroredTitleLabel.textAlignment = .center
        mirroredTitleLabel.numberOfLines = 0
        mirroredTitleLabel.translatesAutoresizingMaskIntoConstraints = false
        mirroredView.addSubview(mirroredTitleLabel)

        // Create mirrored subtitle label
        let mirroredSubtitleLabel = UILabel()
        mirroredSubtitleLabel.text = "🎯 AirPlay Custom View Mirroring"
        mirroredSubtitleLabel.textColor = .white.withAlphaComponent(0.8)
        mirroredSubtitleLabel.font = UIFont.systemFont(ofSize: 24)
        mirroredSubtitleLabel.textAlignment = .center
        mirroredSubtitleLabel.numberOfLines = 0
        mirroredSubtitleLabel.translatesAutoresizingMaskIntoConstraints = false
        mirroredView.addSubview(mirroredSubtitleLabel)

        // Create mirrored animated view
        let mirroredAnimatedView = UIView()
        mirroredAnimatedView.backgroundColor = .white.withAlphaComponent(0.3)
        mirroredAnimatedView.layer.cornerRadius = 40
        mirroredAnimatedView.translatesAutoresizingMaskIntoConstraints = false
        mirroredView.addSubview(mirroredAnimatedView)

        // Create mirrored counter label
        let mirroredCounterLabel = UILabel()
        mirroredCounterLabel.text = counterLabel.text
        mirroredCounterLabel.textColor = .white
        mirroredCounterLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 32, weight: .medium)
        mirroredCounterLabel.textAlignment = .center
        mirroredCounterLabel.translatesAutoresizingMaskIntoConstraints = false
        mirroredView.addSubview(mirroredCounterLabel)

        // Create mirrored progress view
        let mirroredProgressView = UIProgressView(progressViewStyle: .default)
        mirroredProgressView.progressTintColor = .white
        mirroredProgressView.trackTintColor = .white.withAlphaComponent(0.3)
        mirroredProgressView.progress = progressView.progress
        mirroredProgressView.transform = CGAffineTransform(scaleX: 1, y: 3)
        mirroredProgressView.translatesAutoresizingMaskIntoConstraints = false
        mirroredView.addSubview(mirroredProgressView)

        // Setup constraints
        NSLayoutConstraint.activate([
            // Title label
            mirroredTitleLabel.topAnchor.constraint(equalTo: mirroredView.topAnchor, constant: 40),
            mirroredTitleLabel.leadingAnchor.constraint(equalTo: mirroredView.leadingAnchor, constant: 30),
            mirroredTitleLabel.trailingAnchor.constraint(equalTo: mirroredView.trailingAnchor, constant: -30),

            // Subtitle label
            mirroredSubtitleLabel.topAnchor.constraint(equalTo: mirroredTitleLabel.bottomAnchor, constant: 15),
            mirroredSubtitleLabel.leadingAnchor.constraint(equalTo: mirroredView.leadingAnchor, constant: 30),
            mirroredSubtitleLabel.trailingAnchor.constraint(equalTo: mirroredView.trailingAnchor, constant: -30),

            // Animated view
            mirroredAnimatedView.centerXAnchor.constraint(equalTo: mirroredView.centerXAnchor),
            mirroredAnimatedView.centerYAnchor.constraint(equalTo: mirroredView.centerYAnchor),
            mirroredAnimatedView.widthAnchor.constraint(equalToConstant: 80),
            mirroredAnimatedView.heightAnchor.constraint(equalToConstant: 80),

            // Counter label
            mirroredCounterLabel.topAnchor.constraint(equalTo: mirroredAnimatedView.bottomAnchor, constant: 30),
            mirroredCounterLabel.leadingAnchor.constraint(equalTo: mirroredView.leadingAnchor, constant: 30),
            mirroredCounterLabel.trailingAnchor.constraint(equalTo: mirroredView.trailingAnchor, constant: -30),

            // Progress view
            mirroredProgressView.topAnchor.constraint(equalTo: mirroredCounterLabel.bottomAnchor, constant: 25),
            mirroredProgressView.leadingAnchor.constraint(equalTo: mirroredView.leadingAnchor, constant: 30),
            mirroredProgressView.trailingAnchor.constraint(equalTo: mirroredView.trailingAnchor, constant: -30)
        ])

        return mirroredView
    }

    // MARK: - Screen Notifications
    @objc private func screenDidConnect(_ notification: Notification) {
        print("External screen connected")
        // Automatically start mirroring when screen connects
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if self.externalWindow == nil {
                self.startMirroring()
            }
        }
    }

    @objc private func screenDidDisconnect(_ notification: Notification) {
        print("External screen disconnected")
        stopMirroring()
    }

    // MARK: - Helper Methods
    private func showAlert(title: String, message: String) {
        guard let viewController = findViewController() else { return }

        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        viewController.present(alert, animated: true)
    }

    private func findViewController() -> UIViewController? {
        var responder: UIResponder? = self
        while responder != nil {
            if let viewController = responder as? UIViewController {
                return viewController
            }
            responder = responder?.next
        }
        return nil
    }

    // MARK: - Public Methods
    func updateCustomContent(title: String? = nil, subtitle: String? = nil, backgroundColor: UIColor? = nil) {
        if let title = title {
            titleLabel.text = title
        }

        if let subtitle = subtitle {
            subtitleLabel.text = subtitle
        }

        if let backgroundColor = backgroundColor {
            UIView.animate(withDuration: 0.3) {
                self.backgroundColorView.backgroundColor = backgroundColor
            }
        }

        // Update mirrored content if mirroring is active
        if externalWindow != nil {
            updateMirroredContent()
        }
    }

    private func updateMirroredContent() {
        // This would update the mirrored content in real-time
        // For simplicity, we'll recreate the mirrored view
        guard let externalViewController = externalViewController else { return }

        // Remove old mirrored view
        externalViewController.view.subviews.forEach { $0.removeFromSuperview() }

        // Add updated mirrored view
        let updatedMirroredView = createMirroredContentView()
        externalViewController.view.addSubview(updatedMirroredView)

        updatedMirroredView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            updatedMirroredView.centerXAnchor.constraint(equalTo: externalViewController.view.centerXAnchor),
            updatedMirroredView.centerYAnchor.constraint(equalTo: externalViewController.view.centerYAnchor),
            updatedMirroredView.widthAnchor.constraint(equalToConstant: 600),
            updatedMirroredView.heightAnchor.constraint(equalToConstant: 400)
        ])
    }
}
