# iceAirPlay - iOS AirPlay 视频播放器

一个支持AirPlay功能的iOS视频播放器应用，可以将视频内容投屏到Apple TV或其他兼容的AirPlay设备。

## 功能特性

### 🎥 视频播放
- 支持多种视频格式（MP4、MOV等）
- 自定义视频播放器界面
- 播放/暂停控制
- 进度条拖拽控制
- 时间显示（当前时间/总时长）

### 📡 AirPlay 支持
- 一键投屏到Apple TV
- 支持AirPlay兼容设备
- 自动检测可用的AirPlay设备
- 投屏状态指示

### 🎛️ 媒体控制
- 播放/暂停按钮
- 快进/快退（10秒）
- 音量控制滑块
- 静音/取消静音
- 进度条拖拽

### 🔗 视频源
- 支持网络视频URL
- 内置示例视频
- 自定义视频URL输入

## 系统要求

- iOS 14.0 或更高版本
- Xcode 15.0 或更高版本
- Swift 5.0
- 支持WiFi的设备（AirPlay功能需要）

## 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd iceAirPlay
   ```

2. **打开项目**
   ```bash
   open iceAirPlay.xcodeproj
   ```

3. **配置开发者账号**
   - 在Xcode中选择你的开发者团队
   - 修改Bundle Identifier为你的唯一标识符

4. **运行应用**
   - 选择目标设备或模拟器
   - 点击运行按钮（⌘+R）

## 使用说明

### 基本播放
1. 启动应用后，会自动加载默认示例视频
2. 点击播放按钮开始播放
3. 使用进度条控制播放进度
4. 使用音量滑块调节音量

### AirPlay投屏
1. 确保你的iOS设备和Apple TV在同一WiFi网络
2. 点击右上角的AirPlay按钮（📡图标）
3. 选择目标设备
4. 视频将开始在选定设备上播放

### 自定义视频
1. 在URL输入框中输入视频链接
2. 点击"加载视频"按钮
3. 或者点击示例按钮快速加载预设视频

### 媒体控制
- **播放/暂停**: 点击中央的播放/暂停按钮
- **快退**: 点击⏪按钮（后退10秒）
- **快进**: 点击⏩按钮（前进10秒）
- **音量**: 拖拽左上角的音量滑块
- **静音**: 点击🔊/🔇按钮

## 技术实现

### 核心技术
- **AVFoundation**: 视频播放和AirPlay支持
- **AVPlayer**: 媒体播放引擎
- **AVRoutePickerView**: AirPlay设备选择
- **AVAudioSession**: 音频会话管理

### 主要组件
- `AirPlayVideoPlayer`: 自定义视频播放器视图
- `ViewController`: 主界面控制器
- `AppDelegate`: 应用生命周期和音频会话配置

### AirPlay配置
应用已配置以下AirPlay相关设置：
- 音频会话类别设置为播放模式
- 启用AirPlay和蓝牙音频选项
- 配置本地网络访问权限
- 添加Bonjour服务支持

## 故障排除

### AirPlay无法工作
1. 确保设备连接到同一WiFi网络
2. 检查Apple TV是否开启AirPlay功能
3. 重启应用或设备
4. 确认网络权限已授予应用

### 视频无法播放
1. 检查视频URL是否有效
2. 确认网络连接正常
3. 尝试使用示例视频测试
4. 检查视频格式是否支持

### 音频问题
1. 检查设备音量设置
2. 确认应用内音量控制
3. 检查静音开关状态

## 开发说明

### 项目结构
```
iceAirPlay/
├── iceAirPlay.xcodeproj/     # Xcode项目文件
├── iceAirPlay/               # 源代码目录
│   ├── AppDelegate.swift     # 应用委托
│   ├── SceneDelegate.swift   # 场景委托
│   ├── ViewController.swift  # 主视图控制器
│   ├── AirPlayVideoPlayer.swift # 自定义播放器
│   ├── Info.plist           # 应用配置
│   ├── Assets.xcassets/     # 资源文件
│   └── Base.lproj/          # 本地化资源
└── README.md                # 说明文档
```

### 扩展功能
可以考虑添加的功能：
- 播放列表支持
- 视频下载功能
- 字幕支持
- 播放历史记录
- 全屏播放模式
- 画中画支持

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request

---

**注意**: 使用AirPlay功能需要在真实设备上测试，模拟器不支持AirPlay功能。
