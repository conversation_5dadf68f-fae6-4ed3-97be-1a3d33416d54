import XCTest
@testable import iceAirPlay

class iceAirPlayTests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    func testAirPlayVideoPlayerInitialization() throws {
        // 测试AirPlayVideoPlayer的初始化
        let frame = CGRect(x: 0, y: 0, width: 300, height: 200)
        let videoPlayer = AirPlayVideoPlayer(frame: frame)
        
        XCTAssertNotNil(videoPlayer)
        XCTAssertEqual(videoPlayer.frame, frame)
        XCTAssertEqual(videoPlayer.backgroundColor, .black)
    }
    
    func testViewControllerInitialization() throws {
        // 测试ViewController的初始化
        let viewController = ViewController()
        
        XCTAssertNotNil(viewController)
        
        // 加载视图
        viewController.loadViewIfNeeded()
        
        XCTAssertNotNil(viewController.view)
        XCTAssertEqual(viewController.view.backgroundColor, .systemBackground)
    }
    
    func testVideoURLValidation() throws {
        // 测试视频URL的有效性
        let validURLs = [
            "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
            "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
            "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4"
        ]
        
        for urlString in validURLs {
            let url = URL(string: urlString)
            XCTAssertNotNil(url, "URL should be valid: \(urlString)")
        }
    }
    
    func testAudioSessionConfiguration() throws {
        // 测试音频会话配置
        let appDelegate = AppDelegate()
        
        // 这个测试需要在真实设备上运行，因为模拟器可能不支持所有音频会话功能
        // 我们只测试方法调用不会崩溃
        XCTAssertNoThrow(appDelegate.application(UIApplication.shared, didFinishLaunchingWithOptions: nil))
    }

    func testPerformanceExample() throws {
        // This is an example of a performance test case.
        self.measure {
            // Put the code you want to measure the time of here.
            let frame = CGRect(x: 0, y: 0, width: 300, height: 200)
            let _ = AirPlayVideoPlayer(frame: frame)
        }
    }
}
